import { Model } from '@nozbe/watermelondb';
import { field, relation, date, text } from '@nozbe/watermelondb/decorators';

// Define message types
export type MessageType = 'text' | 'media' | 'clear_chat' | 'typing' | 'delete_user' | 'emoji_reaction' | 'system';

// Define media types
export type MediaType = 'image' | 'video' | 'audio' | 'document';

export class Chat extends Model {
  static table = 'chats';

  @text('room_id') roomId!: string;
  @text('sender_username') senderUsername!: string;
  @text('receiver_username') receiverUsername!: string;
  @text('message') message!: string;
  @text('type') type!: MessageType;
  @date('timestamp') timestamp!: number;
  @text('status') status!: string;
  @field('is_mine') isMine!: boolean;

  // Media fields
  @text('media_id') mediaId?: string;
  @text('media_url') mediaUrl?: string;
  @text('media_type') mediaType?: MediaType;
  @text('media_filename') mediaFilename?: string;
  @field('media_file_size') mediaFileSize?: number;
  @text('media_local_path') mediaLocalPath?: string;
  @text('media_thumbnail_path') mediaThumbnailPath?: string;
  @field('media_width') mediaWidth?: number;
  @field('media_height') mediaHeight?: number;
  @field('media_duration') mediaDuration?: number;

  // Helper methods
  getFormattedTime(): string {
    return new Date(this.timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getFormattedDate(): string {
    return new Date(this.timestamp).toLocaleDateString();
  }

  // Convert to a plain object for Redux
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      room_id: this.roomId,
      sender_username: this.senderUsername,
      receiver_username: this.receiverUsername,
      message: this.message,
      type: this.type,
      timestamp: this.timestamp,
      status: this.status,
      is_mine: this.isMine,
      // Media fields
      media_id: this.mediaId,
      media_url: this.mediaUrl,
      media_type: this.mediaType,
      media_filename: this.mediaFilename,
      media_file_size: this.mediaFileSize,
      media_local_path: this.mediaLocalPath,
      media_thumbnail_path: this.mediaThumbnailPath,
      media_width: this.mediaWidth,
      media_height: this.mediaHeight,
      media_duration: this.mediaDuration,
    };
  }
}
