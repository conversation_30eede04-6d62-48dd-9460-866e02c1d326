"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const media_service_1 = require("./media.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const media_entity_1 = require("./media.entity");
let MediaController = class MediaController {
    mediaService;
    constructor(mediaService) {
        this.mediaService = mediaService;
    }
    async uploadMedia(file, req) {
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        const username = req.user.username;
        return this.mediaService.uploadMedia(file, username);
    }
    async getMediaFile(id, res) {
        const { media, buffer } = await this.mediaService.getMediaFile(id);
        res.set({
            'Content-Type': media.mime_type,
            'Content-Length': media.file_size.toString(),
            'Content-Disposition': `inline; filename="${media.original_filename}"`,
            'Cache-Control': 'public, max-age=31536000',
        });
        res.send(buffer);
    }
    async getThumbnail(id, res) {
        const { media, buffer } = await this.mediaService.getThumbnail(id);
        res.set({
            'Content-Type': 'image/jpeg',
            'Content-Length': buffer.length.toString(),
            'Cache-Control': 'public, max-age=31536000',
        });
        res.send(buffer);
    }
    async getMediaInfo(id) {
        return this.mediaService.getMedia(id);
    }
    async deleteMedia(id, req) {
        await this.mediaService.deleteMedia(id);
        return { message: 'Media deleted successfully' };
    }
    async getMyMedia(req) {
        const username = req.user.username;
        return this.mediaService.getMediaByUser(username);
    }
};
exports.MediaController = MediaController;
__decorate([
    (0, common_1.Post)('upload'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload a media file' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        description: 'Media file to upload',
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Media uploaded successfully',
        type: media_entity_1.Media,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid file or file too large' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MediaController.prototype, "uploadMedia", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get media file' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Media file retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Media not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MediaController.prototype, "getMediaFile", null);
__decorate([
    (0, common_1.Get)(':id/thumbnail'),
    (0, swagger_1.ApiOperation)({ summary: 'Get media thumbnail' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Thumbnail retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Thumbnail not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MediaController.prototype, "getThumbnail", null);
__decorate([
    (0, common_1.Get)(':id/info'),
    (0, swagger_1.ApiOperation)({ summary: 'Get media information' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Media information retrieved successfully',
        type: media_entity_1.Media,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Media not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MediaController.prototype, "getMediaInfo", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a media file' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Media deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Media not found' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MediaController.prototype, "deleteMedia", null);
__decorate([
    (0, common_1.Get)('user/my-media'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all media uploaded by the current user' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User media retrieved successfully',
        type: [media_entity_1.Media],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MediaController.prototype, "getMyMedia", null);
exports.MediaController = MediaController = __decorate([
    (0, swagger_1.ApiTags)('media'),
    (0, common_1.Controller)('api/media'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [media_service_1.MediaService])
], MediaController);
//# sourceMappingURL=media.controller.js.map