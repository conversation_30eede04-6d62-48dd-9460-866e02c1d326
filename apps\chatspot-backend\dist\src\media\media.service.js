"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const media_entity_1 = require("./media.entity");
const fs = require("fs");
const path = require("path");
const sharp = require("sharp");
let MediaService = class MediaService {
    mediaRepository;
    uploadDir = 'uploads/media';
    thumbnailDir = 'uploads/thumbnails';
    maxFileSize = 10 * 1024 * 1024;
    constructor(mediaRepository) {
        this.mediaRepository = mediaRepository;
        this.ensureDirectoryExists(this.uploadDir);
        this.ensureDirectoryExists(this.thumbnailDir);
    }
    ensureDirectoryExists(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
    getMediaType(mimeType) {
        if (mimeType.startsWith('image/'))
            return 'image';
        if (mimeType.startsWith('video/'))
            return 'video';
        if (mimeType.startsWith('audio/'))
            return 'audio';
        return 'document';
    }
    isValidMimeType(mimeType) {
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',
            'video/mp4', 'video/mov', 'video/avi', 'video/quicktime',
            'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/m4a',
            'application/pdf', 'text/plain', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];
        return allowedTypes.includes(mimeType);
    }
    async uploadMedia(file, uploadedBy) {
        if (file.size > this.maxFileSize) {
            throw new common_1.BadRequestException(`File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`);
        }
        if (!this.isValidMimeType(file.mimetype)) {
            throw new common_1.BadRequestException('Unsupported file type');
        }
        const mediaType = this.getMediaType(file.mimetype);
        const fileExtension = path.extname(file.originalname);
        const filename = `${Date.now()}-${Math.random().toString(36).substring(2)}${fileExtension}`;
        const filePath = path.join(this.uploadDir, filename);
        fs.writeFileSync(filePath, file.buffer);
        let width = null;
        let height = null;
        let thumbnailPath = null;
        if (mediaType === 'image') {
            try {
                const metadata = await sharp(file.buffer).metadata();
                width = metadata.width || null;
                height = metadata.height || null;
                const thumbnailFilename = `thumb_${filename}`;
                thumbnailPath = path.join(this.thumbnailDir, thumbnailFilename);
                await sharp(file.buffer)
                    .resize(300, 300, { fit: 'inside', withoutEnlargement: true })
                    .jpeg({ quality: 80 })
                    .toFile(thumbnailPath);
            }
            catch (error) {
                console.error('Error processing image:', error);
            }
        }
        const media = this.mediaRepository.create({
            original_filename: file.originalname,
            filename,
            mime_type: file.mimetype,
            file_size: file.size,
            media_type: mediaType,
            file_path: filePath,
            uploaded_by: uploadedBy,
            width,
            height,
            duration: null,
            thumbnail_path: thumbnailPath,
        });
        return this.mediaRepository.save(media);
    }
    async getMedia(id) {
        const media = await this.mediaRepository.findOne({ where: { id } });
        if (!media) {
            throw new common_1.NotFoundException('Media not found');
        }
        return media;
    }
    async getMediaFile(id) {
        const media = await this.getMedia(id);
        if (!fs.existsSync(media.file_path)) {
            throw new common_1.NotFoundException('Media file not found on disk');
        }
        const buffer = fs.readFileSync(media.file_path);
        return { media, buffer };
    }
    async getThumbnail(id) {
        const media = await this.getMedia(id);
        if (!media.thumbnail_path || !fs.existsSync(media.thumbnail_path)) {
            throw new common_1.NotFoundException('Thumbnail not found');
        }
        const buffer = fs.readFileSync(media.thumbnail_path);
        return { media, buffer };
    }
    async deleteMedia(id) {
        const media = await this.getMedia(id);
        if (fs.existsSync(media.file_path)) {
            fs.unlinkSync(media.file_path);
        }
        if (media.thumbnail_path && fs.existsSync(media.thumbnail_path)) {
            fs.unlinkSync(media.thumbnail_path);
        }
        await this.mediaRepository.delete(id);
    }
    async getMediaByUser(username) {
        return this.mediaRepository.find({
            where: { uploaded_by: username },
            order: { uploaded_at: 'DESC' },
        });
    }
};
exports.MediaService = MediaService;
exports.MediaService = MediaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(media_entity_1.Media)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], MediaService);
//# sourceMappingURL=media.service.js.map