import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Media, MediaType } from './media.entity';
import * as fs from 'fs';
import * as path from 'path';
import * as sharp from 'sharp';

@Injectable()
export class MediaService {
  private readonly uploadDir = 'uploads/media';
  private readonly thumbnailDir = 'uploads/thumbnails';
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB

  constructor(
    @InjectRepository(Media)
    private mediaRepository: Repository<Media>,
  ) {
    // Ensure upload directories exist
    this.ensureDirectoryExists(this.uploadDir);
    this.ensureDirectoryExists(this.thumbnailDir);
  }

  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  private getMediaType(mimeType: string): MediaType {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return 'document';
  }

  private isValidMimeType(mimeType: string): boolean {
    const allowedTypes = [
      // Images
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif',
      // Videos
      'video/mp4', 'video/mov', 'video/avi', 'video/quicktime',
      // Audio
      'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/aac', 'audio/m4a',
      // Documents
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    return allowedTypes.includes(mimeType);
  }

  async uploadMedia(
    file: Express.Multer.File,
    uploadedBy: string,
  ): Promise<Media> {
    // Validate file size
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(
        `File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`,
      );
    }

    // Validate MIME type
    if (!this.isValidMimeType(file.mimetype)) {
      throw new BadRequestException('Unsupported file type');
    }

    const mediaType = this.getMediaType(file.mimetype);
    const fileExtension = path.extname(file.originalname);
    const filename = `${Date.now()}-${Math.random().toString(36).substring(2)}${fileExtension}`;
    const filePath = path.join(this.uploadDir, filename);

    // Save file to disk
    fs.writeFileSync(filePath, file.buffer);

    let width: number | null = null;
    let height: number | null = null;
    let thumbnailPath: string | null = null;

    // Process images
    if (mediaType === 'image') {
      try {
        const metadata = await sharp(file.buffer).metadata();
        width = metadata.width || null;
        height = metadata.height || null;

        // Create thumbnail for images
        const thumbnailFilename = `thumb_${filename}`;
        thumbnailPath = path.join(this.thumbnailDir, thumbnailFilename);
        
        await sharp(file.buffer)
          .resize(300, 300, { fit: 'inside', withoutEnlargement: true })
          .jpeg({ quality: 80 })
          .toFile(thumbnailPath);
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    // Create media record
    const media = this.mediaRepository.create({
      original_filename: file.originalname,
      filename,
      mime_type: file.mimetype,
      file_size: file.size,
      media_type: mediaType,
      file_path: filePath,
      uploaded_by: uploadedBy,
      width,
      height,
      duration: null, // TODO: Extract duration for video/audio files
      thumbnail_path: thumbnailPath,
    });

    return this.mediaRepository.save(media);
  }

  async getMedia(id: string): Promise<Media> {
    const media = await this.mediaRepository.findOne({ where: { id } });
    if (!media) {
      throw new NotFoundException('Media not found');
    }
    return media;
  }

  async getMediaFile(id: string): Promise<{ media: Media; buffer: Buffer }> {
    const media = await this.getMedia(id);
    
    if (!fs.existsSync(media.file_path)) {
      throw new NotFoundException('Media file not found on disk');
    }

    const buffer = fs.readFileSync(media.file_path);
    return { media, buffer };
  }

  async getThumbnail(id: string): Promise<{ media: Media; buffer: Buffer }> {
    const media = await this.getMedia(id);
    
    if (!media.thumbnail_path || !fs.existsSync(media.thumbnail_path)) {
      throw new NotFoundException('Thumbnail not found');
    }

    const buffer = fs.readFileSync(media.thumbnail_path);
    return { media, buffer };
  }

  async deleteMedia(id: string): Promise<void> {
    const media = await this.getMedia(id);
    
    // Delete files from disk
    if (fs.existsSync(media.file_path)) {
      fs.unlinkSync(media.file_path);
    }
    
    if (media.thumbnail_path && fs.existsSync(media.thumbnail_path)) {
      fs.unlinkSync(media.thumbnail_path);
    }

    // Delete from database
    await this.mediaRepository.delete(id);
  }

  async getMediaByUser(username: string): Promise<Media[]> {
    return this.mediaRepository.find({
      where: { uploaded_by: username },
      order: { uploaded_at: 'DESC' },
    });
  }
}
