export declare class Message {
    id: string;
    sender_username: string;
    receiver_username: string;
    message: string;
    timestamp: Date;
    status: 'sent' | 'delivered';
    delivered_at: Date | null;
    type: 'text' | 'media' | 'clear_chat' | 'typing' | 'delete_user' | 'system';
    client_message_id: string | null;
    media_id: string | null;
    media_url: string | null;
    media_type: string | null;
    media_filename: string | null;
    media_file_size: number | null;
}
