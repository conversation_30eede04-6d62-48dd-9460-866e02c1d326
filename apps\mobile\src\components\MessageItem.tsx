import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { radius, spacing, typography, useTheme, shadows } from '../theme';
import MediaViewer from './MediaViewer';

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [showMediaViewer, setShowMediaViewer] = useState(false);



  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <View style={styles.systemMessageContainer}>
        <View style={[
          styles.systemMessageContent,
          message.status === 'sending' && styles.sendingGradient
        ]}>
          <Text style={styles.systemMessageText}>
            Chat cleared by {message.is_mine ? 'you' : (message.sender_username || 'Unknown User')}
          </Text>
          <Text style={styles.messageTime}>{formatTime(message.timestamp)}</Text>
        </View>
      </View>
    );
  }

  if (message.type === 'typing') return null;

  const renderMediaContent = () => {
    if (message.type !== 'media' || !message.media_url) return null;

    const isImage = message.media_type?.startsWith('image/');
    const isVideo = message.media_type?.startsWith('video/');
    const isAudio = message.media_type?.startsWith('audio/');
    const isDocument = !isImage && !isVideo && !isAudio;

    if (isImage) {
      return (
        <TouchableOpacity
          style={styles.mediaContainer}
          onPress={() => setShowMediaViewer(true)}
        >
          <Image
            source={{ uri: message.media_url }}
            style={styles.imageMedia}
            resizeMode="cover"
          />
          {message.media_filename && (
            <Text style={styles.mediaFilename} numberOfLines={1}>
              {message.media_filename}
            </Text>
          )}
        </TouchableOpacity>
      );
    }

    if (isVideo) {
      return (
        <TouchableOpacity
          style={styles.mediaContainer}
          onPress={() => setShowMediaViewer(true)}
        >
          <View style={styles.videoContainer}>
            <Image
              source={{ uri: message.media_thumbnail_path || message.media_url }}
              style={styles.videoThumbnail}
              resizeMode="cover"
            />
            <View style={styles.playButton}>
              <Text style={styles.playIcon}>▶️</Text>
            </View>
          </View>
          {message.media_filename && (
            <Text style={styles.mediaFilename} numberOfLines={1}>
              {message.media_filename}
            </Text>
          )}
        </TouchableOpacity>
      );
    }

    if (isAudio) {
      return (
        <TouchableOpacity
          style={styles.audioContainer}
          onPress={() => setShowMediaViewer(true)}
        >
          <Text style={styles.audioIcon}>🎵</Text>
          <View style={styles.audioInfo}>
            <Text style={styles.audioFilename} numberOfLines={1}>
              {message.media_filename || 'Audio'}
            </Text>
            {message.media_duration && (
              <Text style={styles.audioDuration}>
                {Math.floor(message.media_duration / 60)}:
                {String(Math.floor(message.media_duration % 60)).padStart(2, '0')}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      );
    }

    // Document
    return (
      <TouchableOpacity
        style={styles.documentContainer}
        onPress={() => setShowMediaViewer(true)}
      >
        <Text style={styles.documentIcon}>📄</Text>
        <View style={styles.documentInfo}>
          <Text style={styles.documentFilename} numberOfLines={1}>
            {message.media_filename || 'Document'}
          </Text>
          {message.media_file_size && (
            <Text style={styles.documentSize}>
              {(message.media_file_size / 1024 / 1024).toFixed(1)} MB
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderStatusIcon = () => {
    if (!message.isMine && !message.is_mine) return null;

    switch (message.status) {
      case 'sending':
        return (
          <View style={styles.statusContainer}>
            <ActivityIndicator size="small" color={colors.myMessageTick} />
          </View>
        );
      case 'sent':
        return (
          <View style={styles.statusContainer}>
            <Text style={[styles.singleTick, { color: colors.myMessageTick }]}>✓</Text>
          </View>
        );
      case 'delivered':
      case 'read':
        return (
          <View style={styles.statusContainer}>
            <Text style={[styles.doubleTick, { color: colors.success }]}>✓✓</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const isMine = message.isMine || message.is_mine;

  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient
      ]}>
        {/* Render media content if it's a media message */}
        {message.type === 'media' && renderMediaContent()}

        {/* Render text content if present */}
        {message.message && (
          <Text style={[
            styles.messageText,
            isMine ? styles.sentText : styles.receivedText,
            message.type === 'media' && styles.mediaCaption
          ]}>
            {message.message}
          </Text>
        )}

        <View style={styles.messageInfo}>
          <Text style={[
            styles.messageTime,
            { color: isMine ? colors.myMessageTime : colors.peerMessageTime }
          ]}>
            {formatTime(message.timestamp)}
          </Text>
          {renderStatusIcon()}
        </View>
      </View>

      {/* Media Viewer Modal */}
      {showMediaViewer && message.type === 'media' && (
        <MediaViewer
          visible={showMediaViewer}
          mediaUrl={message.media_url}
          mediaType={message.media_type}
          mediaFilename={message.media_filename}
          onClose={() => setShowMediaViewer(false)}
        />
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 3,
    marginHorizontal: spacing.md,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: spacing.sm,
  },
  messageContent: {
  maxWidth: '80%',
  borderRadius: radius.md,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
},
  sentContent: {
    backgroundColor: colors.primaryLight3, // Use new message-specific colors
      // ✅ Cross-platform shadow
  shadowColor: colors.primaryColor,
  shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 3, // for Android only
  },
  receivedContent: {
    backgroundColor: colors.toneLight3, // Use new message-specific colors
    shadowColor: colors.toneColor,
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 3, // for Android only
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    lineHeight: 20,
  },
  sentText: {
    color: colors.myMessageText, // Use new message-specific colors
  },
  receivedText: {
    color: colors.peerMessageText, // Use new message-specific colors
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  messageTime: {
    marginRight: spacing.xs,
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    lineHeight: 16,
    color:colors.toneLight3,
  },
  statusContainer: {
    marginLeft: spacing.xs,
  },
  singleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  doubleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  systemMessageContent: {
    backgroundColor: colors.gray100,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    textAlign: 'center',
  },
  // Media styles
  mediaContainer: {
    marginBottom: spacing.xs,
  },
  imageMedia: {
    width: screenWidth * 0.6,
    height: screenWidth * 0.6 * 0.75, // 4:3 aspect ratio
    borderRadius: radius.sm,
  },
  videoContainer: {
    position: 'relative',
  },
  videoThumbnail: {
    width: screenWidth * 0.6,
    height: screenWidth * 0.6 * 0.75,
    borderRadius: radius.sm,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playIcon: {
    fontSize: 16,
    color: 'white',
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.gray100,
    borderRadius: radius.sm,
    width: screenWidth * 0.6,
  },
  audioIcon: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  audioInfo: {
    flex: 1,
  },
  audioFilename: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.text,
  },
  audioDuration: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    marginTop: 2,
  },
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.gray100,
    borderRadius: radius.sm,
    width: screenWidth * 0.6,
  },
  documentIcon: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  documentInfo: {
    flex: 1,
  },
  documentFilename: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.text,
  },
  documentSize: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    marginTop: 2,
  },
  mediaFilename: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  mediaCaption: {
    marginTop: spacing.xs,
    fontSize: 14,
  },
});

export default MessageItem;
