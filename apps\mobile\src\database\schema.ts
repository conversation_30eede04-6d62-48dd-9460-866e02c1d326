import { appSchema, tableSchema } from '@nozbe/watermelondb';

export const schema = appSchema({
  version: 3, // Updated to version 3 to add media support
  tables: [
    // Chats table - represents individual messages
    tableSchema({
      name: 'chats',
      columns: [
        { name: 'room_id', type: 'string', isIndexed: true },
        { name: 'sender_username', type: 'string', isIndexed: true },
        { name: 'receiver_username', type: 'string', isIndexed: true },
        { name: 'message', type: 'string' },
        { name: 'type', type: 'string' },
        { name: 'timestamp', type: 'number', isIndexed: true },
        { name: 'status', type: 'string' },
        { name: 'is_mine', type: 'boolean' },
        { name: 'client_message_id', type: 'string', isOptional: true, isIndexed: true },
        // Media fields
        { name: 'media_id', type: 'string', isOptional: true },
        { name: 'media_url', type: 'string', isOptional: true },
        { name: 'media_type', type: 'string', isOptional: true },
        { name: 'media_filename', type: 'string', isOptional: true },
        { name: 'media_file_size', type: 'number', isOptional: true },
        { name: 'media_local_path', type: 'string', isOptional: true },
        { name: 'media_thumbnail_path', type: 'string', isOptional: true },
        { name: 'media_width', type: 'number', isOptional: true },
        { name: 'media_height', type: 'number', isOptional: true },
        { name: 'media_duration', type: 'number', isOptional: true }
      ]
    }),

    // Rooms table - represents active conversations
    tableSchema({
      name: 'rooms',
      columns: [
        { name: 'room_id', type: 'string', isIndexed: true },
        { name: 'username', type: 'string', isIndexed: true },
        { name: 'last_msg', type: 'string' },
        { name: 'updated', type: 'number', isIndexed: true },
        { name: 'unread_count', type: 'number' },
      ]
    }),
  ]
});
