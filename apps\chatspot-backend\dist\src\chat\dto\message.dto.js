"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class MessageDto {
    id;
    sender_username;
    receiver_username;
    message;
    timestamp;
    status;
    delivered_at;
    type;
    client_message_id;
    media_id;
    media_url;
    media_type;
    media_filename;
    media_file_size;
}
exports.MessageDto = MessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the message',
        example: '123e4567-e89b-12d3-a456-426614174000',
    }),
    __metadata("design:type", String)
], MessageDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username of the sender',
        example: 'johndoe',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MessageDto.prototype, "sender_username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username of the receiver',
        example: 'janedoe',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MessageDto.prototype, "receiver_username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message content',
        example: 'Hello, how are you?',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MessageDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the message was sent',
        example: '2023-10-15T14:30:00Z',
    }),
    __metadata("design:type", Date)
], MessageDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the message',
        example: 'delivered',
        enum: ['sent', 'delivered'],
    }),
    __metadata("design:type", String)
], MessageDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the message was delivered',
        example: '2023-10-15T14:30:05Z',
        nullable: true,
    }),
    __metadata("design:type", Object)
], MessageDto.prototype, "delivered_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message type',
        example: 'text',
        enum: ['text', 'media', 'clear_chat', 'typing', 'delete_user', 'system'],
        default: 'text',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MessageDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Client-generated message ID for tracking',
        example: 'client-msg-123',
        nullable: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], MessageDto.prototype, "client_message_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Media file ID if this is a media message',
        example: '123e4567-e89b-12d3-a456-426614174000',
        nullable: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], MessageDto.prototype, "media_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Media file URL for quick access',
        example: '/api/media/123e4567-e89b-12d3-a456-426614174000',
        nullable: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], MessageDto.prototype, "media_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Media type (image, video, audio, document)',
        example: 'image',
        nullable: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], MessageDto.prototype, "media_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Original filename of the media',
        example: 'vacation_photo.jpg',
        nullable: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], MessageDto.prototype, "media_filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File size in bytes',
        example: 2048576,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], MessageDto.prototype, "media_file_size", void 0);
//# sourceMappingURL=message.dto.js.map