import { Response } from 'express';
import { MediaService } from './media.service';
import { Media } from './media.entity';
export declare class MediaController {
    private readonly mediaService;
    constructor(mediaService: MediaService);
    uploadMedia(file: Express.Multer.File, req: any): Promise<Media>;
    getMediaFile(id: string, res: Response): Promise<void>;
    getThumbnail(id: string, res: Response): Promise<void>;
    getMediaInfo(id: string): Promise<Media>;
    deleteMedia(id: string, req: any): Promise<{
        message: string;
    }>;
    getMyMedia(req: any): Promise<Media[]>;
}
