import { Repository } from 'typeorm';
import { Media } from './media.entity';
export declare class MediaService {
    private mediaRepository;
    private readonly uploadDir;
    private readonly thumbnailDir;
    private readonly maxFileSize;
    constructor(mediaRepository: Repository<Media>);
    private ensureDirectoryExists;
    private getMediaType;
    private isValidMimeType;
    uploadMedia(file: Express.Multer.File, uploadedBy: string): Promise<Media>;
    getMedia(id: string): Promise<Media>;
    getMediaFile(id: string): Promise<{
        media: Media;
        buffer: Buffer;
    }>;
    getThumbnail(id: string): Promise<{
        media: Media;
        buffer: Buffer;
    }>;
    deleteMedia(id: string): Promise<void>;
    getMediaByUser(username: string): Promise<Media[]>;
}
