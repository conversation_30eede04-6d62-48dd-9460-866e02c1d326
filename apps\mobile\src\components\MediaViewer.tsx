import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import Video from 'react-native-video';
import RNFS from 'react-native-fs';
import { useTheme } from '../theme';

interface MediaViewerProps {
  visible: boolean;
  mediaUrl: string;
  mediaType: string;
  mediaFilename: string;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const MediaViewer: React.FC<MediaViewerProps> = ({
  visible,
  mediaUrl,
  mediaType,
  mediaFilename,
  onClose,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [isLoading, setIsLoading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  const isImage = mediaType.startsWith('image/');
  const isVideo = mediaType.startsWith('video/');
  const isAudio = mediaType.startsWith('audio/');
  const isDocument = !isImage && !isVideo && !isAudio;

  const handleDownload = async () => {
    try {
      setIsLoading(true);
      setDownloadProgress(0);

      const downloadDest = `${RNFS.DownloadDirectoryPath}/${mediaFilename}`;
      
      const downloadOptions = {
        fromUrl: mediaUrl,
        toFile: downloadDest,
        progress: (res: any) => {
          const progress = (res.bytesWritten / res.contentLength) * 100;
          setDownloadProgress(progress);
        },
      };

      const result = await RNFS.downloadFile(downloadOptions).promise;
      
      if (result.statusCode === 200) {
        Alert.alert('Success', `File saved to Downloads folder`);
      } else {
        Alert.alert('Error', 'Failed to download file');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to download file');
    } finally {
      setIsLoading(false);
      setDownloadProgress(0);
    }
  };

  const renderMedia = () => {
    if (isImage) {
      return (
        <Image
          source={{ uri: mediaUrl }}
          style={styles.image}
          resizeMode="contain"
        />
      );
    }

    if (isVideo) {
      return (
        <Video
          source={{ uri: mediaUrl }}
          style={styles.video}
          controls={true}
          resizeMode="contain"
          paused={false}
        />
      );
    }

    if (isAudio) {
      return (
        <View style={styles.audioContainer}>
          <Text style={styles.audioIcon}>🎵</Text>
          <Text style={styles.audioTitle}>{mediaFilename}</Text>
          <Video
            source={{ uri: mediaUrl }}
            style={styles.audioPlayer}
            controls={true}
            audioOnly={true}
          />
        </View>
      );
    }

    // Document or other file types
    return (
      <View style={styles.documentContainer}>
        <Text style={styles.documentIcon}>📄</Text>
        <Text style={styles.documentTitle}>{mediaFilename}</Text>
        <Text style={styles.documentSubtitle}>Tap download to view</Text>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.filename} numberOfLines={1}>
            {mediaFilename}
          </Text>
          <TouchableOpacity
            style={styles.downloadButton}
            onPress={handleDownload}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Text style={styles.downloadText}>⬇</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Media Content */}
        <View style={styles.mediaContainer}>
          {renderMedia()}
        </View>

        {/* Download Progress */}
        {isLoading && downloadProgress > 0 && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${downloadProgress}%` },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {Math.round(downloadProgress)}%
            </Text>
          </View>
        )}
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    color: colors.white,
    fontSize: 20,
    fontWeight: 'bold',
  },
  filename: {
    flex: 1,
    color: colors.white,
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  downloadButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  downloadText: {
    color: colors.white,
    fontSize: 20,
  },
  mediaContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: screenWidth,
    height: screenHeight - 150,
  },
  video: {
    width: screenWidth,
    height: screenHeight - 150,
  },
  audioContainer: {
    alignItems: 'center',
    padding: 40,
  },
  audioIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  audioTitle: {
    color: colors.white,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 30,
  },
  audioPlayer: {
    width: screenWidth - 40,
    height: 60,
  },
  documentContainer: {
    alignItems: 'center',
    padding: 40,
  },
  documentIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  documentTitle: {
    color: colors.white,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 10,
  },
  documentSubtitle: {
    color: colors.gray300,
    fontSize: 14,
    textAlign: 'center',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 50,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: colors.gray600,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  progressText: {
    color: colors.white,
    fontSize: 12,
  },
});

export default MediaViewer;
